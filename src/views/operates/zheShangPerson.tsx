import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useStore, useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, A, OrderColumn, Drawer } from '@components/common';
import { CommonObject } from '@app/types';
import { getCrumb, setMenuHook } from '@app/utils/utils';
import { Row, Col, Input, Divider, Button, Select, Icon, Modal, message, Timeline } from 'antd';
import { getTableList } from '@app/action/tableList';
import { opApi as api, releaseListApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@components/permItems';
import Radio from 'antd/es/radio';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import AssistantForm from './assistantForm';
import { render } from 'react-dom';
import ReactClipboard from 'react-clipboardjs-copy';
import AddZheShangPerson from './addZheShangPerson';
// import ImagePreviewColumn from "@app/components/common/imagePreviewColumn"; // 暂时不需要，因为移除了头像列

const defaultSize = 10;
export default function PartyNewsMgr(props: any) {
  const [filter, setFilter] = useState<any>({
    name: '',
    status: '',
  });

  const [search, setSearch] = useState('');
  const dispatch = useDispatch();
  const store = useStore();
  const history = useHistory();
  const {
    total,
    current,
    size = 10,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  const { loading, run } = useXHR();
  const [logs, setLogs] = useState<any>({
    visible: false,
    logs: [],
    title: '',
  });

  const [drawer, setDrawer] = useState<any>({
    visible: false,
    skey: 0,
    record: null,
  });

  // ✅ 添加操作模式状态管理
  const [operationType, setOperationType] = useState<'add' | 'edit'>('add');
  const [isLoading, setIsLoading] = useState(false);

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const getList = useCallback(
    (goFirstPage: boolean = false, overlap: CommonObject = {}) => {
      const relCurrent = goFirstPage ? 1 : current;
      const map: any = {};
      for (const key in filter) {
        if (filter[key]) {
          map[key] = filter[key];
        }
      }
      dispatch(
        getTableList('getZheShangPersonList', 'page', {
          ...map,
          current: relCurrent,
          size,
          ...overlap,
        })
      );
    },
    [current, filter, size, dispatch]
  );

  const editRecord = (record: any) => {
    // ✅ 设置为编辑模式
    setOperationType('edit');
    run(api.getZheShangPersonDetail, { zj_business_person_id: record.zj_business_person_id }, true)
      .then((response: any) => {
        const { data } = response;
        setDrawer({
          skey: Date.now(),
          visible: true,
          record: data.zjBusinessPerson,
        });
      })
      .catch((e: any) => {
        console.error('获取详情失败:', e);
      });
  };
  // ✅ 智能分页处理：删除后检查当前页数据状态
  const handleSmartPagination = useCallback(() => {
    // 🐛 计算删除后当前页剩余数据量
    const currentPageDataCount = records.length;
    const isLastItemOnPage = currentPageDataCount === 1;
    const isNotFirstPage = current > 1;

    if (isLastItemOnPage && isNotFirstPage) {
      // ⚡ 当前页删除后无数据且不是第一页，自动回退到上一页
      const targetPage = current - 1;
      console.log(`智能分页：当前页(${current})删除后无数据，自动回退到第${targetPage}页`);

      // 更新分页参数并重新获取数据
      const map: any = {};
      for (const key in filter) {
        if (filter[key]) {
          map[key] = filter[key];
        }
      }

      dispatch(
        getTableList('getZheShangPersonList', 'page', {
          ...map,
          current: targetPage,
          size,
        })
      );
    } else {
      // ⚡ 当前页仍有数据或已是第一页，刷新当前页
      getList(false); // 保持当前页码
    }
  }, [records.length, current, filter, size, dispatch, getList]);

  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确定要删除吗？`,
      onOk: () => {
        run(
          api.deleteZheShangPerson,
          { zj_business_person_id: record.zj_business_person_id },
          true
        ).then(() => {
          message.success('操作成功');
          // ✅ 删除成功后执行智能分页处理
          handleSmartPagination();
        });
      },
    });
  };

  const updateRecordStatus = (record: any) => {
    run(
      api.offlineZheShangPerson,
      {
        zj_business_person_id: record.zj_business_person_id,
        status: record.status == 1 ? 0 : 1,
      },
      true
    ).then(() => {
      message.success('操作成功');
      getList();
    });
  };

  const getColumns = () => {
    let values = [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '姓名',
        dataIndex: 'name',
      },
      // 注释掉头像列，因为浙商人物接口不返回头像字段
      // {
      //   title: '头像',
      //   dataIndex: 'avatar',
      //   width: 100,
      //   render: (text: any, record: any) => (<div style={{ height: 60, textAlign: 'center' }}>
      //     <ImagePreviewColumn text={text} imgs={[text]}></ImagePreviewColumn>
      //   </div>)
      // },
      {
        title: '内容数',
        dataIndex: 'article_count',
        render: (text: any) => text || 0,
      },
      {
        title: '状态',
        dataIndex: 'status',
        render: (text: any) => (text == 0 ? '下架' : '上架'),
      },
      {
        title: '创建时间',
        dataIndex: 'created_at_str',
        width: 150,
        render: (text: any) => {
          return text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-';
        },
      },
      {
        title: '操作人',
        dataIndex: 'updated_by_str',
        width: 130,
      },
      {
        title: '操作时间',
        dataIndex: 'updated_at_str',
        width: 150,
        render: (text: any, record: any) => {
          return (
            <A onClick={() => getOperateLog(record)} style={{ whiteSpace: 'normal' }}>
              <div>{moment(text).format('YYYY-MM-DD') + ' ' + moment(text).format('HH:mm:ss')}</div>
            </A>
          );
        },
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any, i: number) => (
          <span>
            <PermA perm="zj_business:update_status" onClick={() => updateRecordStatus(record)}>
              {record.status == 1 ? '下架' : '上架'}
            </PermA>
            <Divider type="vertical" />
            <PermA perm="zj_business:update" onClick={() => editRecord(record)}>
              编辑
            </PermA>
            <Divider type="vertical" />
            <PermA perm="zj_business:delete" onClick={() => deleteRecord(record)}>
              删除
            </PermA>
            <Divider type="vertical" />
            <ReactClipboard
              action="copy"
              text={record.url}
              onSuccess={() => message.success('链接已复制')}
              onError={() => message.error('复制失败')}
            >
              <a>复制链接</a>
            </ReactClipboard>
          </span>
        ),
        width: 220,
      },
    ];

    return values;
  };

  useEffect(() => {
    // getList({ current: 1, size: 10 });
    setMenuHook(dispatch, props);
  }, []);

  useEffect(() => {
    getList(true);
  }, [filter]);

  const getOperateLog = (record: any) => {
    api
      .getZheShangPersonLog({ type: '200810', target_id: record.zj_business_person_id })
      .then((r: any) => {
        setLogs({
          visible: true,
          logs: r.data.admin_log_list,
          title: record.name,
          key: Date.now(),
        });
      })
      .catch();
  };

  const addRecord = () => {
    // ✅ 设置为添加模式
    setOperationType('add');
    setDrawer({
      skey: Date.now(),
      visible: true,
      record: null,
    });
  };

  const handleSearch = () => {
    setFilter({
      ...filter,
      name: search,
    });
  };

  // ✅ 操作完成后的处理函数
  const handleOperationComplete = useCallback(() => {
    try {
      setIsLoading(true);

      if (operationType === 'add') {
        // 🐛 添加操作完成：刷新列表并重置到第一页
        getList(true); // goFirstPage = true
      } else if (operationType === 'edit') {
        // ⚡ 编辑操作完成：刷新列表但保持当前分页
        getList(false); // goFirstPage = false，保持当前页
      }

      // 重置状态
      setDrawer({ visible: false });
      setOperationType('add');
    } catch (error) {
      console.error('操作完成处理失败:', error);
    } finally {
      setIsLoading(false);
    }
  }, [operationType, getList]);

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton
            perm="zj_business:create"
            style={{ marginRight: 8 }}
            onClick={addRecord}
            loading={isLoading && operationType === 'add'}
          >
            <Icon type="plus-circle" /> 添加人物
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={12}>
            <Select
              value={filter.status}
              style={{ width: 120 }}
              onChange={(v) => setFilter({ ...filter, status: v })}
            >
              <Select.Option value="">全部状态</Select.Option>
              <Select.Option value={'0'}>下架</Select.Option>
              <Select.Option value={'1'}>上架</Select.Option>
            </Select>
          </Col>

          <Col span={12}>
            <div className="title-right-col">
              <Input
                style={{ width: 160, marginRight: 8 }}
                onPressEnter={handleSearch}
                value={search}
                placeholder="请输入人物姓名"
                onChange={(e) => setSearch(e.target.value)}
              />
              <Button onClick={handleSearch}>
                <Icon type="search" />
                搜索
              </Button>
            </div>
          </Col>
        </Row>
        <Table
          func="getZheShangPersonList"
          index="page"
          filter={filter}
          columns={getColumns()}
          rowKey="id"
          pagination={true}
        />

        <AddZheShangPerson
          {...drawer}
          onClose={() => {
            // ✅ 关闭时重置状态
            setDrawer({ visible: false });
            setOperationType('add');
          }}
          onEnd={handleOperationComplete}
        ></AddZheShangPerson>

        <Modal
          visible={logs.visible}
          title={"操作日志"}
          key={logs.key}
          cancelText={null}
          onCancel={() => setLogs({ visible: false })}
          onOk={() => setLogs({ visible: false })}
        >
          <p>姓名：{logs.title}</p>
          <br />
          <div>
            <Timeline>
              {logs?.logs?.map((v: any, i: number) => [
                <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                  &nbsp;
                </Timeline.Item>,
                v.log_list.map((logItem: any, index: number) => (
                  <Timeline.Item
                    className="timeline-dot"
                    data-show={moment(logItem.created_at).format('HH:mm:ss')}
                    key={`time${i}-action${index}`}
                  >
                    {logItem.admin_name}&emsp;&emsp;{logItem.remark}
                    &emsp;&emsp;
                  </Timeline.Item>
                )),
              ])}
            </Timeline>
          </div>
        </Modal>
      </div>
    </>
  );
}
